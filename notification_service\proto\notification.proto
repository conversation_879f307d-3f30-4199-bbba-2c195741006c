syntax = "proto3";

package notification;

option go_package = "github.com/olzzhas/edunite-server/notification_service/proto";

import "google/protobuf/timestamp.proto";

// Notification service definition
service NotificationService {
    // Create a new notification
    rpc CreateNotification(CreateNotificationRequest) returns (NotificationResponse);
    
    // Get notifications for a user
    rpc GetUserNotifications(GetUserNotificationsRequest) returns (GetUserNotificationsResponse);
    
    // Get all notifications (admin only)
    rpc GetAllNotifications(GetAllNotificationsRequest) returns (GetAllNotificationsResponse);
    
    // Mark notification as read
    rpc MarkAsRead(MarkAsReadRequest) returns (MarkAsReadResponse);
    
    // Delete notification
    rpc DeleteNotification(DeleteNotificationRequest) returns (DeleteNotificationResponse);
    
    // Send scheduled notifications
    rpc SendScheduledNotifications(SendScheduledNotificationsRequest) returns (SendScheduledNotificationsResponse);
    
    // Get notification statistics
    rpc GetNotificationStats(GetNotificationStatsRequest) returns (GetNotificationStatsResponse);
}

// Enums
enum NotificationType {
    INFO = 0;
    WARNING = 1;
    SUCCESS = 2;
    ERROR = 3;
    ANNOUNCEMENT = 4;
}

enum NotificationPriority {
    LOW = 0;
    NORMAL = 1;
    HIGH = 2;
    URGENT = 3;
}

enum TargetType {
    ALL = 0;
    ROLE = 1;
    USER = 2;
    DEGREE = 3;
    COURSE = 4;
    THREAD = 5;
}

// Messages
message Notification {
    int64 id = 1;
    string title = 2;
    string message = 3;
    NotificationType type = 4;
    NotificationPriority priority = 5;
    TargetType target_type = 6;
    string target_value = 7;
    int64 sender_id = 8;
    bool send_email = 9;
    string email_subject = 10;
    string email_template = 11;
    google.protobuf.Timestamp scheduled_at = 12;
    google.protobuf.Timestamp sent_at = 13;
    google.protobuf.Timestamp created_at = 14;
    google.protobuf.Timestamp updated_at = 15;
}

message NotificationRecipient {
    int64 id = 1;
    int64 notification_id = 2;
    int64 user_id = 3;
    bool is_read = 4;
    google.protobuf.Timestamp read_at = 5;
    bool email_sent = 6;
    google.protobuf.Timestamp email_sent_at = 7;
    google.protobuf.Timestamp created_at = 8;
    google.protobuf.Timestamp updated_at = 9;
}

message UserNotification {
    Notification notification = 1;
    NotificationRecipient recipient = 2;
}

// Request/Response messages
message CreateNotificationRequest {
    string title = 1;
    string message = 2;
    NotificationType type = 3;
    NotificationPriority priority = 4;
    TargetType target_type = 5;
    string target_value = 6;
    int64 sender_id = 7;
    bool send_email = 8;
    string email_subject = 9;
    string email_template = 10;
    google.protobuf.Timestamp scheduled_at = 11;
}

message NotificationResponse {
    Notification notification = 1;
    string message = 2;
    bool success = 3;
}

message GetUserNotificationsRequest {
    int64 user_id = 1;
    int32 page = 2;
    int32 limit = 3;
    bool unread_only = 4;
}

message GetUserNotificationsResponse {
    repeated UserNotification notifications = 1;
    int32 total_count = 2;
    int32 unread_count = 3;
}

message GetAllNotificationsRequest {
    int32 page = 1;
    int32 limit = 2;
    TargetType target_type = 3;
    string target_value = 4;
}

message GetAllNotificationsResponse {
    repeated Notification notifications = 1;
    int32 total_count = 2;
}

message MarkAsReadRequest {
    int64 notification_id = 1;
    int64 user_id = 2;
}

message MarkAsReadResponse {
    bool success = 1;
    string message = 2;
}

message DeleteNotificationRequest {
    int64 notification_id = 1;
}

message DeleteNotificationResponse {
    bool success = 1;
    string message = 2;
}

message SendScheduledNotificationsRequest {
    // Empty - will send all scheduled notifications that are due
}

message SendScheduledNotificationsResponse {
    int32 sent_count = 1;
    string message = 2;
}

message GetNotificationStatsRequest {
    int64 user_id = 1; // If 0, get global stats (admin only)
}

message GetNotificationStatsResponse {
    int32 total_notifications = 1;
    int32 unread_notifications = 2;
    int32 read_notifications = 3;
    int32 email_notifications = 4;
}
