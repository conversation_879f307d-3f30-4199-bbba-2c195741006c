# API для управления программами обучения - Degree Courses

## Что это такое?

Добавлена новая система управления **программами обучения (degree programs)**. Теперь можно:

1. **Связывать курсы с программами** - определять какие курсы входят в конкретную программу обучения
2. **Контролировать доступ студентов** - студенты видят только те курсы, которые входят в их программу
3. **Настраивать обязательность курсов** - отмечать какие курсы обязательные, а какие факультативные
4. **Планировать по семестрам** - указывать в каком семестре должен изучаться курс

## Зачем это нужно?

**Проблема:** Раньше все студенты видели все курсы, что создавало путаницу.

**Решение:** Теперь каждый студент видит только курсы своей программы обучения.

**Пример:**

-  Студент программы "Информатика" видит: Алгоритмы, Базы данных, Веб-разработка
-  Студент программы "Дизайн" видит: Фотошоп, Иллюстратор, UX/UI дизайн

## Как это работает?

1. **Администратор** создает программы обучения (degrees)
2. **Администратор** добавляет курсы к программам через новые API
3. **Студентам** назначается программа обучения
4. **Студенты** видят только курсы своей программы

## Новые эндпоинты

### 1. Добавить курс к программе

**POST** `/degree-courses`

**Headers:**

```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body:**

```json
{
   "degree_id": 1,
   "course_id": 2,
   "is_required": true,
   "semester_number": 1
}
```

**Response (201):**

```json
{
   "id": 1,
   "degree_id": 1,
   "course_id": 2,
   "is_required": true,
   "semester_number": 1,
   "created_at": {
      "seconds": 1749739135,
      "nanos": 975903000
   },
   "updated_at": {
      "seconds": 1749739135,
      "nanos": 975903000
   }
}
```

**cURL:**

```bash
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 2,
    "is_required": true,
    "semester_number": 1
  }'
```

---

### 2. Получить курсы программы

**GET** `/degree-courses/degree/{degree_id}/courses`

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Response (200):**

```json
{
   "courses": [
      {
         "id": 1,
         "title": "Основы программирования",
         "description": "Введение в программирование на Python",
         "banner_image_url": "course-images/python-basics.jpg",
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         },
         "updated_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      },
      {
         "id": 2,
         "title": "Веб-разработка",
         "description": "HTML, CSS, JavaScript",
         "banner_image_url": "course-images/web-dev.jpg",
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         },
         "updated_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      }
   ]
}
```

**cURL:**

```bash
curl -X GET http://localhost:8081/degree-courses/degree/1/courses \
  -H "Authorization: Bearer <admin_token>"
```

---

### 3. Удалить курс из программы

**DELETE** `/degree-courses/degree/{degree_id}/course/{course_id}`

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Response (200):**

```json
{
   "message": "Course removed from degree successfully"
}
```

**cURL:**

```bash
curl -X DELETE http://localhost:8081/degree-courses/degree/1/course/2 \
  -H "Authorization: Bearer <admin_token>"
```

---

### 4. Получить доступные курсы для студента

**GET** `/students/{user_id}/available-courses`

**Headers:**

```
Authorization: Bearer <token>
```

**Response (200):**

```json
{
   "courses": [
      {
         "id": 1,
         "title": "Доступный курс",
         "description": "Курс доступен студенту",
         "banner_image_url": "course-images/available.jpg",
         "created_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         },
         "updated_at": {
            "seconds": 1749739135,
            "nanos": 975903000
         }
      }
   ]
}
```

**cURL:**

```bash
curl -X GET http://localhost:8081/students/123/available-courses \
  -H "Authorization: Bearer <token>"
```

---

### 5. Получить доступные потоки для студента

**GET** `/students/{user_id}/available-threads`

Возвращает только те потоки, которые доступны студенту на основе его программы обучения. Структура ответа точно такая же, как у `/thread` эндпоинта.

**Response (200):**

```json
[
   {
      "available_slots": 26,
      "booked_slots": 0,
      "course": {
         "id": 1,
         "title": "Golang",
         "description": "Golang course provides...",
         "banner_image_url": ""
      },
      "course_id": 1,
      "created_at": {
         "seconds": 1749731576,
         "nanos": 44856000
      },
      "id": 1,
      "max_students": 26,
      "schedules": [
         {
            "id": 1,
            "thread_id": 1,
            "day_of_week": 1,
            "start_time": "09:00:00",
            "end_time": "10:30:00",
            "location": "501",
            "created_at": {
               "seconds": 1749731576,
               "nanos": 60708000
            },
            "updated_at": {
               "seconds": 1749731576,
               "nanos": 60708000
            }
         }
      ],
      "semester_id": 1,
      "syllabus_url": "",
      "teacher": {
         "id": 3,
         "name": "Ruslan",
         "surname": "Omarov",
         "email": "<EMAIL>"
      },
      "teacher_id": 3,
      "title": "IT-2106",
      "updated_at": {
         "seconds": 1749731576,
         "nanos": 44856000
      }
   }
]
```

**cURL:**

```bash
curl -X GET http://localhost:8081/students/123/available-threads
```

**Особенности:**

-  Возвращает только потоки курсов, к которым у студента есть доступ согласно его программе обучения
-  Если у студента нет доступных курсов, возвращается пустой массив `[]`
-  Структура ответа идентична `/thread` эндпоинту
-  Включает полную информацию о курсе, преподавателе и расписаниях

## Параметры

### Для добавления курса к программе:

-  `degree_id` (int, обязательно) - ID программы обучения
-  `course_id` (int, обязательно) - ID курса
-  `is_required` (bool, опционально) - Обязательный курс (по умолчанию false)
-  `semester_number` (int, опционально) - Номер семестра

## Коды ошибок

-  `400` - Неверные данные запроса
-  `401` - Требуется авторизация
-  `403` - Недостаточно прав или студент не имеет доступа к курсу
-  `404` - Ресурс не найден
-  `409` - Конфликт (например, уже зарегистрирован)
-  `412` - Предварительные условия не выполнены
-  `500` - Внутренняя ошибка сервера

## Важные изменения в обработке ошибок

**Регистрация на поток (`POST /thread/register`):**

-  `403` - Студент не имеет доступа к курсу на основе программы обучения
-  `409` - Студент уже зарегистрирован на поток
-  `404` - Поток не найден
-  `412` - Не выполнены предварительные условия

**Пример ошибки доступа:**

```json
{
   "error": "student does not have access to this course based on their degree program"
}
```

## Доступ

-  Эндпоинты `/degree-courses/*` - только администраторы
-  Эндпоинт `/students/{user_id}/available-courses` - аутентифицированные пользователи
