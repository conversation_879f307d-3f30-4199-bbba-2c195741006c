package main

import (
	"github.com/gin-contrib/cors"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"

	"log"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/config"
	"github.com/olzzhas/edunite-server/gateway/handlers"
	"github.com/olzzhas/edunite-server/gateway/routes"
	"google.golang.org/grpc"
)

func main() {
	r := gin.Default()
	cfg := config.LoadConfig()

	corsConfig := cors.Config{
		AllowOrigins: []string{
			"http://***********:4200",
			"http://************:4200",
			"http://*************:4200",
			"http://*************:4200",
			"http://localhost:4200",
			"http://************:4200",
			"http://***************",
			"http://localhost:5173",
			"http://localhost:5174",
			"http://localhost:5175",
			"http://localhost:8081",
			"http://localhost:8082",
			"http://localhost:8083",
			"http://localhost:8084",
		},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}

	r.Use(cors.New(corsConfig))

	// Connect to Auth Service
	authConn, err := grpc.Dial(cfg.Services.AuthService.Target, grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to Auth Service: %v", err)
	}
	defer authConn.Close()
	authClient := clients.NewAuthClient(authConn)

	// ----------------------------
	// Подключение к User Service
	// ----------------------------
	userConn, err := grpc.Dial(cfg.Services.UserService.Target, grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to User Service: %v", err)
	}
	defer userConn.Close()
	userServiceClient := clients.NewUserClient(userConn)

	// ----------------------------
	// Подключение к Logger Service
	// ----------------------------
	loggerConn, err := grpc.Dial(cfg.Services.LoggerService.Target, grpc.WithInsecure())
	if err != nil {
		log.Printf("Warning: Failed to connect to Logger Service: %v", err)
		log.Println("Using temporary logger client instead")
	}
	defer func() {
		if loggerConn != nil {
			loggerConn.Close()
		}
	}()

	// ----------------------------
	// Подключение к Course Service
	// ----------------------------
	courseConn, err := grpc.Dial(cfg.Services.CourseService.Target, grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to Course Service: %v", err)
	}
	log.Println("Course Service Connected")
	defer courseConn.Close()

	// Create logger client for fetching logs
	loggerClient := clients.NewLoggerClient(loggerConn)

	// Инициализация RabbitMQ publisher или fallback logger
	var logPublisher interface {
		PublishLog(level, message, service string, data map[string]any) error
		Close()
	}

	rabbitPublisher, err := clients.NewRabbitLogPublisher(cfg.Services.LoggerService.RabbitMQTarget)
	if err != nil {
		log.Printf("Warning: Failed to init rabbit log publisher: %v", err)
		log.Println("Using fallback logger (stdout) instead")
		logPublisher = clients.NewFallbackLogPublisher()
	} else {
		logPublisher = rabbitPublisher
		defer rabbitPublisher.Close()
	}

	pbAssignmentClient := assignmentpb.NewAssignmentServiceClient(courseConn)

	courseServiceClient := clients.NewCourseClient(courseConn)
	threadServiceClient := clients.NewThreadClient(courseConn)
	semesterServiceClient := clients.NewSemesterClient(courseConn)
	assignmentServiceClient := clients.NewAssignmentClient(pbAssignmentClient)
	attendanceServiceClient := clients.NewAttendanceClient(courseConn)
	transcriptServiceClient := clients.NewTranscriptClient(courseConn)

	// ----------------------------
	// Подключение к Storage Service
	// ----------------------------

	storageConn, err := grpc.Dial(cfg.Services.StorageService.Target, grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to Storage Service: %v", err)
	}
	defer storageConn.Close()
	storageServiceClient := clients.NewStorageClient(storageConn)

	// ----------------------------
	// Подключение к Sport Service
	// ----------------------------

	sportConn, err := grpc.Dial(cfg.Services.SportService.Target, grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to Sport Service: %v", err)
	}
	defer sportConn.Close()

	sportTypeClient := clients.NewSportTypeClient(sportConn)
	facilityClient := clients.NewFacilityClient(sportConn)
	scheduleClient := clients.NewScheduleClient(sportConn)
	bookingClient := clients.NewBookingClient(sportConn)
	medicalCertificateClient := clients.NewMedicalCertificateClient(sportConn)
	semesterLimitClient := clients.NewSemesterLimitClient(sportConn)
	physicalEducationClient := clients.NewPhysicalEducationClient(sportConn)
	// ----------------------------
	// Инициализация обработчиков
	// ----------------------------
	authHandler := &handlers.AuthHandler{
		RabbitLogPublisher: logPublisher,
		AuthClient:         authClient,
		UserService:        userServiceClient,
	}

	userHandler := &handlers.UserHandler{
		RabbitLogPublisher: logPublisher,
		UserService:        userServiceClient,
	}

	// Initialize role-based handlers
	adminHandler := handlers.NewAdminHandler(userServiceClient)
	teacherHandler := handlers.NewTeacherHandler(
		assignmentServiceClient,
		threadServiceClient,
		userServiceClient,
		courseServiceClient,
		logPublisher,
	)
	moderatorHandler := handlers.NewModeratorHandler()

	courseHandler := &handlers.CourseHandler{
		RabbitLogPublisher: logPublisher,
		CourseService:      courseServiceClient,
		StorageService:     storageServiceClient,
	}

	degreeCourseHandler := handlers.NewDegreeCourseHandler(courseServiceClient.GetClient())

	storageHandler := &handlers.StorageHandler{
		RabbitLogPublisher: logPublisher,
		StorageClient:      storageServiceClient,
	}

	threadHandler := &handlers.ThreadHandler{
		RabbitLogPublisher: logPublisher,
		StorageService:     storageServiceClient,
		CourseService:      courseServiceClient,
		ThreadService:      threadServiceClient,
		AttendanceService:  attendanceServiceClient,
		SemesterService:    semesterServiceClient,
		UserService:        userServiceClient,
		TranscriptService:  transcriptServiceClient,
	}

	semesterHandler := &handlers.SemesterHandler{
		RabbitLogPublisher: logPublisher,
		SemesterService:    semesterServiceClient,
	}

	assignmentHandler := &handlers.AssignmentHandler{
		AssignmentService:  assignmentServiceClient,
		UserService:        userServiceClient,
		StorageService:     storageServiceClient,
		RabbitLogPublisher: logPublisher,
	}

	attendanceHandler := &handlers.AttendanceHandler{
		RabbitLogPublisher: logPublisher,
		AttendanceService:  attendanceServiceClient,
	}

	logHandler := handlers.NewLogHandler(logPublisher, loggerClient)

	sportHandler := &handlers.SportHandler{
		RabbitLogPublisher:       logPublisher,
		SportTypeClient:          sportTypeClient,
		FacilityClient:           facilityClient,
		ScheduleClient:           scheduleClient,
		BookingClient:            bookingClient,
		MedicalCertificateClient: medicalCertificateClient,
		SemesterLimitClient:      semesterLimitClient,
		PhysicalEducationClient:  physicalEducationClient,
	}

	studentHandler := handlers.NewStudentHandler(
		threadServiceClient,
		scheduleClient,
		scheduleClient, // Using the same client for sport schedules
		bookingClient,
		assignmentServiceClient,
		logPublisher,
	)

	locationHandler := &handlers.LocationHandler{
		RabbitLogPublisher: logPublisher,
		ThreadService:      threadServiceClient,
	}

	transcriptHandler := handlers.NewTranscriptHandler(transcriptServiceClient.GetClient())

	// ----------------------------
	// Настраиваем маршруты
	// ----------------------------
	routes.SetupAuthRoutes(r, authHandler)                             // Auth
	routes.SetupUserRoutes(r, authClient, userHandler)                 // User
	routes.SetupCourseRoutes(r, authClient, courseHandler)             // Course
	routes.SetupStorageRoutes(r, authClient, storageHandler)           // Storage
	routes.SetupThreadRoutes(r, threadHandler)                         // Thread
	routes.SetupSemesterRoutes(r, semesterHandler)                     // Semester
	routes.SetupAssignmentRoutes(r, assignmentHandler)                 // Assignment
	routes.SetupAttendanceRoutes(r, attendanceHandler)                 // Attendance
	routes.SetupLogRoutes(r, logHandler)                               // Logs
	routes.SetupSportRoutes(r, authClient, sportHandler)               // Sport
	routes.SetupLocationRoutes(r, locationHandler)                     // Locations
	routes.SetupTranscriptRoutes(r, authClient, transcriptHandler)     // Transcript
	routes.SetupDegreeCourseRoutes(r, authClient, degreeCourseHandler) // Degree-Course relationships
	routes.SetupSwaggerRoutes(r)                                       // Swagger Documentation

	// Setup role-based routes
	routes.SetupAdminRoutes(r, authClient, userServiceClient, adminHandler)         // Admin
	routes.SetupTeacherRoutes(r, authClient, userServiceClient, teacherHandler)     // Teacher
	routes.SetupModeratorRoutes(r, authClient, userServiceClient, moderatorHandler) // Moderator
	routes.SetupStudentRoutes(r, authClient, studentHandler, threadHandler)         // Student
	// ----------------------------
	// Запускаем сервер
	// ----------------------------
	if err := r.Run(":8081"); err != nil {
		log.Fatalf("Failed to run server: %v", err)
	}
}
